/** @type {import('tailwindcss').Config} */
export default {
	content: ['./src/**/*.{html,js,svelte,ts}'],
	theme: {
		extend: {
			colors: {
				// Custom color palette for Hitez
				primary: '#F8F9FA', // Off-white background
				secondary: '#1A1A1A', // Dark charcoal text
				accent: {
					DEFAULT: '#8B5CF6', // Violet-500
					hover: '#7C3AED', // Violet-600
					light: '#A78BFA', // Violet-400
				}
			},
			fontFamily: {
				// Custom fonts
				heading: ['Monument Extended', 'system-ui', 'sans-serif'],
				body: ['Inter', 'system-ui', 'sans-serif'],
			},
			fontSize: {
				// Custom typography scale
				'hero': ['clamp(3rem, 8vw, 6rem)', { lineHeight: '1.1' }],
				'section-title': ['clamp(2rem, 5vw, 3.5rem)', { lineHeight: '1.2' }],
				'large': ['clamp(1.25rem, 3vw, 1.5rem)', { lineHeight: '1.4' }],
			},
			spacing: {
				// Custom spacing for sections
				'section': 'clamp(4rem, 10vh, 8rem)',
				'section-sm': 'clamp(2rem, 5vh, 4rem)',
			},
			animation: {
				// Custom animations for GSAP fallbacks
				'fade-in': 'fadeIn 0.6s ease-out',
				'slide-up': 'slideUp 0.8s ease-out',
			},
			keyframes: {
				fadeIn: {
					'0%': { opacity: '0' },
					'100%': { opacity: '1' },
				},
				slideUp: {
					'0%': { opacity: '0', transform: 'translateY(2rem)' },
					'100%': { opacity: '1', transform: 'translateY(0)' },
				},
			},
		},
	},
	plugins: [],
}
