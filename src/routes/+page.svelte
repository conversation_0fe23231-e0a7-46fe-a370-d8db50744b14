<script lang="ts">
	import { onMount } from 'svelte';
	import { gsap } from 'gsap';
	import { ScrollTrigger } from 'gsap/ScrollTrigger';
	import Logo from '$lib/Logo.svelte';

	// Register GSAP plugins
	gsap.registerPlugin(ScrollTrigger);

	onMount(() => {
		// Check for reduced motion preference
		const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

		if (!prefersReducedMotion) {
			// Hero section animations
			const tl = gsap.timeline();

			// Animate logo first
			tl.from('.logo', {
				scale: 0,
				rotation: 180,
				duration: 1,
				ease: 'back.out(1.7)'
			})
			// Then animate heading words with stagger
			.from('.hero-word', {
				y: 100,
				opacity: 0,
				duration: 0.8,
				stagger: 0.2,
				ease: 'power3.out'
			}, '-=0.5')
			// Finally animate paragraph and button
			.from('.hero-text, .hero-button', {
				y: 50,
				opacity: 0,
				duration: 0.6,
				stagger: 0.1,
				ease: 'power2.out'
			}, '-=0.3');

			// Philosophy section animation
			ScrollTrigger.create({
				trigger: '.philosophy-section',
				start: 'top 80%',
				animation: gsap.from('.philosophy-content > *', {
					y: 60,
					opacity: 0,
					duration: 0.8,
					stagger: 0.2,
					ease: 'power2.out'
				})
			});

			// Services section animation
			ScrollTrigger.create({
				trigger: '.services-section',
				start: 'top 80%',
				animation: gsap.from('.service-item', {
					y: 80,
					opacity: 0,
					duration: 0.8,
					stagger: 0.15,
					ease: 'power2.out'
				})
			});

			// Team section with pinned animation - simplified for mobile
			ScrollTrigger.matchMedia({
				"(min-width: 768px)": function() {
					ScrollTrigger.create({
						trigger: '.team-section',
						start: 'top top',
						end: 'bottom bottom',
						pin: '.team-content',
						scrub: 1,
						animation: gsap.timeline()
							.to('.team-member:nth-child(1)', { opacity: 0, x: -100 })
							.to('.team-member:nth-child(2)', { opacity: 1, x: 0 }, '<')
							.to('.team-member:nth-child(2)', { opacity: 0, x: -100 })
							.to('.team-member:nth-child(3)', { opacity: 1, x: 0 }, '<')
					});
				},
				"(max-width: 767px)": function() {
					// Simpler animation for mobile
					ScrollTrigger.create({
						trigger: '.team-section',
						start: 'top 80%',
						animation: gsap.from('.team-member', {
							y: 50,
							opacity: 0,
							duration: 0.8,
							stagger: 0.2,
							ease: 'power2.out'
						})
					});
				}
			});

			// CTA section animation
			ScrollTrigger.create({
				trigger: '.cta-section',
				start: 'top 80%',
				animation: gsap.from('.cta-content > *', {
					y: 50,
					opacity: 0,
					duration: 0.6,
					stagger: 0.1,
					ease: 'power2.out'
				})
			});

			// Button hover animations
			const buttons = document.querySelectorAll('.animated-button');
			buttons.forEach(button => {
				button.addEventListener('mouseenter', () => {
					gsap.to(button, { scale: 1.05, duration: 0.3, ease: 'power2.out' });
				});
				button.addEventListener('mouseleave', () => {
					gsap.to(button, { scale: 1, duration: 0.3, ease: 'power2.out' });
				});
			});
		} else {
			// Fallback for reduced motion - just fade in elements
			gsap.set('.hero-word, .hero-text, .hero-button, .philosophy-content > *, .service-item, .team-member, .cta-content > *', {
				opacity: 1
			});
		}

		// Cleanup function
		return () => {
			ScrollTrigger.getAll().forEach(trigger => trigger.kill());
		};
	});
</script>

<!-- Hero Section -->
<section class="hero-section min-h-screen flex items-center justify-center px-6 py-section" aria-label="Hero section">
	<div class="container mx-auto max-w-4xl text-center">
		<div class="mb-8">
			<Logo class="mx-auto text-violet-500" size={80} />
		</div>

		<h1 class="font-heading text-hero font-bold text-slate-900 mb-6 overflow-hidden">
			<span class="hero-word inline-block">Born</span>
			<span class="hero-word inline-block ml-2 sm:ml-4">Digital.</span>
			<br>
			<span class="hero-word inline-block">Built</span>
			<span class="hero-word inline-block ml-2 sm:ml-4">Creative.</span>
		</h1>

		<p class="hero-text text-large text-slate-600 mb-8 max-w-2xl mx-auto">
			A new generation of creators, blending technology and art to build unforgettable experiences.
		</p>

		<button
			class="hero-button animated-button bg-violet-500 hover:bg-violet-600 text-white px-6 sm:px-8 py-3 sm:py-4 rounded-lg font-medium text-base sm:text-lg transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:ring-offset-2"
			aria-label="Contact us to start creating together"
		>
			Let's Create Together
		</button>
	</div>
</section>

<!-- Philosophy Section -->
<section class="philosophy-section py-section px-6">
	<div class="container mx-auto max-w-4xl">
		<div class="philosophy-content text-center">
			<h2 class="font-heading text-section-title font-bold text-slate-900 mb-8">
				Why We Exist
			</h2>
			<p class="text-large text-slate-600 mb-6 max-w-3xl mx-auto">
				In a world where digital experiences are becoming the norm, we believe creativity shouldn't be constrained by technology—it should be amplified by it.
			</p>
			<p class="text-large text-slate-600 max-w-3xl mx-auto">
				We're here to bridge the gap between imagination and reality, creating experiences that don't just engage—they transform.
			</p>
		</div>
	</div>
</section>

<!-- Services Section -->
<section class="services-section py-section px-6 bg-slate-100">
	<div class="container mx-auto max-w-6xl">
		<div class="text-center mb-16">
			<h2 class="font-heading text-section-title font-bold text-slate-900 mb-6">
				What We Create
			</h2>
			<p class="text-large text-slate-600 max-w-2xl mx-auto">
				From interactive installations to immersive digital experiences, we craft solutions that captivate and inspire.
			</p>
		</div>

		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
			<div class="service-item bg-white p-6 sm:p-8 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
				<div class="w-12 h-12 bg-violet-100 rounded-lg flex items-center justify-center mb-6">
					<svg class="w-6 h-6 text-violet-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
					</svg>
				</div>
				<h3 class="font-heading text-lg sm:text-xl font-semibold text-slate-900 mb-4">Interactive Installations</h3>
				<p class="text-slate-600 text-sm sm:text-base">Immersive physical experiences that respond to human interaction, creating memorable moments that bridge digital and physical worlds.</p>
			</div>

			<div class="service-item bg-white p-6 sm:p-8 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
				<div class="w-12 h-12 bg-violet-100 rounded-lg flex items-center justify-center mb-6">
					<svg class="w-6 h-6 text-violet-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
					</svg>
				</div>
				<h3 class="font-heading text-lg sm:text-xl font-semibold text-slate-900 mb-4">AR/VR Experiences</h3>
				<p class="text-slate-600 text-sm sm:text-base">Cutting-edge augmented and virtual reality solutions that transport audiences to new dimensions of storytelling and engagement.</p>
			</div>

			<div class="service-item bg-white p-6 sm:p-8 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 md:col-span-2 lg:col-span-1">
				<div class="w-12 h-12 bg-violet-100 rounded-lg flex items-center justify-center mb-6">
					<svg class="w-6 h-6 text-violet-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2"></path>
					</svg>
				</div>
				<h3 class="font-heading text-lg sm:text-xl font-semibold text-slate-900 mb-4">Projection Mapping</h3>
				<p class="text-slate-600 text-sm sm:text-base">Transform any surface into a dynamic canvas with precision-mapped visuals that redefine spatial storytelling and brand experiences.</p>
			</div>
		</div>
	</div>
</section>

<!-- Team Section -->
<section class="team-section min-h-screen py-section px-6">
	<div class="container mx-auto max-w-4xl">
		<div class="text-center mb-16">
			<h2 class="font-heading text-section-title font-bold text-slate-900 mb-6">
				Who We Are
			</h2>
			<p class="text-large text-slate-600 max-w-2xl mx-auto">
				A collective of digital natives, creative technologists, and visionary storytellers.
			</p>
		</div>

		<div class="team-content relative h-96">
			<div class="team-member absolute inset-0 flex items-center justify-center opacity-100">
				<div class="text-center">
					<div class="w-32 h-32 bg-gradient-to-br from-violet-400 to-violet-600 rounded-full mx-auto mb-6 flex items-center justify-center">
						<span class="text-white text-4xl font-bold">H</span>
					</div>
					<h3 class="font-heading text-2xl font-semibold text-slate-900 mb-2">The Creators</h3>
					<p class="text-slate-600 max-w-md">Artists and designers who see beyond the conventional, crafting experiences that resonate on an emotional level.</p>
				</div>
			</div>

			<div class="team-member absolute inset-0 flex items-center justify-center opacity-0">
				<div class="text-center">
					<div class="w-32 h-32 bg-gradient-to-br from-violet-400 to-violet-600 rounded-full mx-auto mb-6 flex items-center justify-center">
						<span class="text-white text-4xl font-bold">I</span>
					</div>
					<h3 class="font-heading text-2xl font-semibold text-slate-900 mb-2">The Innovators</h3>
					<p class="text-slate-600 max-w-md">Technologists and engineers who push boundaries, turning impossible ideas into tangible realities.</p>
				</div>
			</div>

			<div class="team-member absolute inset-0 flex items-center justify-center opacity-0">
				<div class="text-center">
					<div class="w-32 h-32 bg-gradient-to-br from-violet-400 to-violet-600 rounded-full mx-auto mb-6 flex items-center justify-center">
						<span class="text-white text-4xl font-bold">Z</span>
					</div>
					<h3 class="font-heading text-2xl font-semibold text-slate-900 mb-2">The Visionaries</h3>
					<p class="text-slate-600 max-w-md">Strategists and storytellers who understand the power of experience to transform brands and connect communities.</p>
				</div>
			</div>
		</div>
	</div>
</section>

<!-- Call to Action Section -->
<section class="cta-section py-section px-6 bg-slate-900 text-white">
	<div class="container mx-auto max-w-4xl">
		<div class="cta-content text-center">
			<h2 class="font-heading text-section-title font-bold mb-6">
				Ready to Create Something Extraordinary?
			</h2>
			<p class="text-large text-slate-300 mb-12 max-w-2xl mx-auto">
				Let's collaborate to bring your vision to life. Whether it's an immersive installation, a digital experience, or something entirely new—we're here to make it happen.
			</p>

			<form class="max-w-md mx-auto space-y-6 mb-8" aria-label="Contact form">
				<div>
					<label for="name" class="sr-only">Your Name</label>
					<input
						id="name"
						type="text"
						placeholder="Your Name"
						class="w-full px-4 py-3 rounded-lg bg-slate-800 border border-slate-700 text-white placeholder-slate-400 focus:outline-none focus:border-violet-500 focus:ring-2 focus:ring-violet-500 transition-colors duration-300"
						required
						aria-required="true"
					>
				</div>
				<div>
					<label for="email" class="sr-only">Your Email</label>
					<input
						id="email"
						type="email"
						placeholder="Your Email"
						class="w-full px-4 py-3 rounded-lg bg-slate-800 border border-slate-700 text-white placeholder-slate-400 focus:outline-none focus:border-violet-500 focus:ring-2 focus:ring-violet-500 transition-colors duration-300"
						required
						aria-required="true"
					>
				</div>
				<div>
					<label for="message" class="sr-only">Project Details</label>
					<textarea
						id="message"
						placeholder="Tell us about your project..."
						rows="4"
						class="w-full px-4 py-3 rounded-lg bg-slate-800 border border-slate-700 text-white placeholder-slate-400 focus:outline-none focus:border-violet-500 focus:ring-2 focus:ring-violet-500 transition-colors duration-300 resize-none"
						required
						aria-required="true"
					></textarea>
				</div>
				<button
					type="submit"
					class="animated-button w-full bg-violet-500 hover:bg-violet-600 text-white px-6 sm:px-8 py-3 sm:py-4 rounded-lg font-medium text-base sm:text-lg transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:ring-offset-2 focus:ring-offset-slate-900"
					aria-label="Submit contact form"
				>
					Start the Conversation
				</button>
			</form>

			<div class="flex justify-center space-x-6 text-slate-400">
				<a href="mailto:<EMAIL>" class="hover:text-violet-400 transition-colors duration-300">
					<EMAIL>
				</a>
				<span>•</span>
				<a href="tel:+84123456789" class="hover:text-violet-400 transition-colors duration-300">
					+84 123 456 789
				</a>
			</div>
		</div>
	</div>
</section>
