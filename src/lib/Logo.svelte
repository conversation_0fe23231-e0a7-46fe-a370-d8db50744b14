<script lang="ts">
	interface Props {
		class?: string;
		size?: number;
	}

	let { class: className = '', size = 60 }: Props = $props();
</script>

<svg
	class="logo {className}"
	width={size}
	height={size}
	viewBox="0 0 100 100"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	aria-label="Hitez Logo"
>
	<!-- Minimalist Z design with clean geometric lines -->
	<g class="logo-group">
		<!-- Top horizontal line -->
		<rect
			class="logo-line logo-top"
			x="20"
			y="25"
			width="60"
			height="8"
			fill="currentColor"
			rx="4"
		/>
		
		<!-- Diagonal line -->
		<rect
			class="logo-line logo-diagonal"
			x="42"
			y="42"
			width="70"
			height="8"
			fill="currentColor"
			rx="4"
			transform="rotate(45 77 46)"
		/>
		
		<!-- Bottom horizontal line -->
		<rect
			class="logo-line logo-bottom"
			x="20"
			y="67"
			width="60"
			height="8"
			fill="currentColor"
			rx="4"
		/>
	</g>
</svg>

<style>
	.logo {
		transition: transform 0.3s ease;
	}
	
	.logo:hover {
		transform: scale(1.05);
	}
	
	.logo-line {
		transform-origin: center;
	}
	
	/* Fallback animation for reduced motion */
	@media (prefers-reduced-motion: no-preference) {
		.logo-group {
			animation: none; /* GSAP will handle animations */
		}
	}
	
	@media (prefers-reduced-motion: reduce) {
		.logo {
			transition: none;
		}
		
		.logo:hover {
			transform: none;
		}
	}
</style>
