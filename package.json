{"name": "hitez", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint ."}, "devDependencies": {"@eslint/compat": "^1.3.1", "@eslint/js": "^9.30.1", "@sveltejs/adapter-auto": "^6.0.1", "@sveltejs/kit": "^2.22.2", "@sveltejs/vite-plugin-svelte": "^5.1.0", "@tailwindcss/vite": "^4.1.11", "@types/node": "^24.0.10", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-svelte": "^3.10.1", "globals": "^16.3.0", "gsap": "^3.13.0", "prettier": "^3.6.2", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.13", "svelte": "^5.35.2", "svelte-check": "^4.2.2", "tailwindcss": "^4.1.11", "typescript": "^5.8.3", "typescript-eslint": "^8.35.1", "vite": "^6.3.5"}}