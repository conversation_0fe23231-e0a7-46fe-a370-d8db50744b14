# Hitez - Born Digital. Built Creative.

A high-performance, minimalist, single-page website for the creative technology agency "Hitez". Built with SvelteKit 5, GSAP animations, and Tailwind CSS.

## 🚀 Features

- **Modern Tech Stack**: SvelteKit 5 with TypeScript, Tailwind CSS 4, and GSAP 3
- **Sophisticated Animations**: GSAP-powered animations with ScrollTrigger for immersive user experience
- **Responsive Design**: Fully responsive across all devices with mobile-first approach
- **Accessibility**: WCAG compliant with keyboard navigation and reduced motion support
- **Performance Optimized**: Lightweight and fast-loading with optimized animations

## 🎨 Design System

- **Colors**: Light theme with violet accent (#8B5CF6), slate backgrounds, and high contrast text
- **Typography**: Inter font family with custom responsive scaling
- **Layout**: Clean, minimalist design with generous whitespace
- **Animations**: Smooth, performance-optimized GSAP animations

## 📱 Sections

1. **Hero**: Animated logo reveal with staggered text animations
2. **Philosophy**: "Why We Exist" with scroll-triggered content reveals
3. **Services**: Grid layout showcasing Interactive Installations, AR/VR, and Projection Mapping
4. **Team**: Pinned scroll animation showcasing the creative collective
5. **Call to Action**: Contact form with animated interactions

## 🛠 Development

### Prerequisites
- Node.js 18+
- npm, pnpm, or yarn

### Getting Started

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Open in browser
npm run dev -- --open
```

### Build for Production

```bash
# Create production build
npm run build

# Preview production build
npm run preview
```

## 🎯 Technical Highlights

- **GSAP Integration**: Animations initialized in `onMount` lifecycle with proper cleanup
- **ScrollTrigger**: Advanced scroll-based animations with mobile optimizations
- **Accessibility**: Screen reader support, keyboard navigation, and motion preferences
- **TypeScript**: Full type safety throughout the application
- **Tailwind CSS**: Utility-first styling with custom configuration

## 📦 Dependencies

- SvelteKit ^2.22.2 (Svelte 5 ^5.35.2)
- Tailwind CSS ^4.1.11
- GSAP ^3.13.0
- TypeScript ^5.8.3
- Vite ^6.3.5

## 🎨 Customization

The design system is configured in `tailwind.config.js` with custom colors, fonts, and spacing. GSAP animations can be customized in the main page component.

## 📄 License

Private project for Hitez creative agency.
